<?php
/**
 * Recharge API Endpoints
 * Handles recharge requests, status updates, and configuration
 */

// Define API access constant
define('API_ACCESS', true);

// Include configuration
require_once '../config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get database connection
$conn = getDbConnection();

// Get request data
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$license_key = $_POST['license_key'] ?? $_GET['license_key'] ?? '';
$device_id = $_POST['device_id'] ?? $_GET['device_id'] ?? '';

// Log API request
error_log("Recharge API Request - Action: $action, License: $license_key, Device: $device_id");

// Validate license and device
function validateLicenseAndDevice($conn, $license_key, $device_id) {
    if (empty($license_key) || empty($device_id)) {
        return false;
    }
    
    // Check if license exists and is valid
    $stmt = $conn->prepare("SELECT * FROM licenses WHERE license_key = ? AND status = 1");
    $stmt->bind_param("s", $license_key);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return false;
    }
    
    // Check if device is authenticated for this license
    $stmt = $conn->prepare("SELECT * FROM auth_logs WHERE license_key = ? AND device_id = ? AND approval_status = 'approved' ORDER BY created_at DESC LIMIT 1");
    $stmt->bind_param("ss", $license_key, $device_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    return $result->num_rows > 0;
}

// Handle different actions
switch ($action) {
    case 'submit_recharge':
        handleSubmitRecharge($conn);
        break;
    
    case 'get_recharge_status':
        handleGetRechargeStatus($conn);
        break;
    
    case 'update_recharge_status':
        handleUpdateRechargeStatus($conn);
        break;
    
    case 'get_operators':
        handleGetOperators($conn);
        break;
    
    case 'get_recharge_settings':
        handleGetRechargeSettings($conn);
        break;
    
    case 'update_recharge_settings':
        handleUpdateRechargeSettings($conn);
        break;
    
    case 'get_recharge_history':
        handleGetRechargeHistory($conn);
        break;
    
    case 'process_sms_response':
        handleProcessSmsResponse($conn);
        break;
    
    default:
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action specified'
        ]);
        break;
}

function handleSubmitRecharge($conn) {
    global $license_key, $device_id;
    
    $phone_number = $_POST['phone_number'] ?? '';
    $amount = $_POST['amount'] ?? '';
    $operator = $_POST['operator'] ?? '';
    $sim_slot = $_POST['sim_slot'] ?? 1;
    
    // Validate input
    if (empty($phone_number) || empty($amount) || empty($operator)) {
        echo json_encode([
            'success' => false,
            'message' => 'Missing required fields'
        ]);
        return;
    }
    
    // Validate license and device
    if (!validateLicenseAndDevice($conn, $license_key, $device_id)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid license or device not authorized'
        ]);
        return;
    }
    
    // Generate unique order ID
    $order_id = 'RCH_' . time() . '_' . rand(1000, 9999);
    
    // Get operator USSD pattern
    $stmt = $conn->prepare("SELECT ussd_pattern FROM operators WHERE code = ? AND status = 1");
    $stmt->bind_param("s", $operator);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid operator selected'
        ]);
        return;
    }
    
    $operator_data = $result->fetch_assoc();
    $ussd_pattern = $operator_data['ussd_pattern'];
    
    // Replace placeholders in USSD pattern
    $ussd_code = str_replace(['{amount}', '{number}'], [$amount, $phone_number], $ussd_pattern);
    
    // Insert recharge request
    $stmt = $conn->prepare("INSERT INTO recharge_logs (license_key, device_id, order_id, phone_number, amount, operator, ussd_code, sim_slot, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')");
    $stmt->bind_param("ssssdssi", $license_key, $device_id, $order_id, $phone_number, $amount, $operator, $ussd_code, $sim_slot);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'Recharge request submitted successfully',
            'order_id' => $order_id,
            'ussd_code' => $ussd_code,
            'sim_slot' => $sim_slot
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to submit recharge request'
        ]);
    }
}

function handleGetRechargeStatus($conn) {
    global $license_key, $device_id;
    
    $order_id = $_GET['order_id'] ?? '';
    
    if (empty($order_id)) {
        echo json_encode([
            'success' => false,
            'message' => 'Order ID required'
        ]);
        return;
    }
    
    // Validate license and device
    if (!validateLicenseAndDevice($conn, $license_key, $device_id)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid license or device not authorized'
        ]);
        return;
    }
    
    $stmt = $conn->prepare("SELECT * FROM recharge_logs WHERE order_id = ? AND license_key = ? AND device_id = ?");
    $stmt->bind_param("sss", $order_id, $license_key, $device_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $recharge = $result->fetch_assoc();
        echo json_encode([
            'success' => true,
            'recharge' => $recharge
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Recharge not found'
        ]);
    }
}

function handleUpdateRechargeStatus($conn) {
    global $license_key, $device_id;
    
    $order_id = $_POST['order_id'] ?? '';
    $status = $_POST['status'] ?? '';
    $api_response = $_POST['api_response'] ?? '';
    $sms_response = $_POST['sms_response'] ?? '';
    
    if (empty($order_id) || empty($status)) {
        echo json_encode([
            'success' => false,
            'message' => 'Order ID and status required'
        ]);
        return;
    }
    
    // Validate license and device
    if (!validateLicenseAndDevice($conn, $license_key, $device_id)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid license or device not authorized'
        ]);
        return;
    }
    
    $stmt = $conn->prepare("UPDATE recharge_logs SET status = ?, api_response = ?, sms_response = ?, updated_at = CURRENT_TIMESTAMP WHERE order_id = ? AND license_key = ? AND device_id = ?");
    $stmt->bind_param("ssssss", $status, $api_response, $sms_response, $order_id, $license_key, $device_id);
    
    if ($stmt->execute() && $stmt->affected_rows > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'Recharge status updated successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update recharge status'
        ]);
    }
}

function handleGetOperators($conn) {
    $country = $_GET['country'] ?? 'BD';
    
    $stmt = $conn->prepare("SELECT * FROM operators WHERE country = ? AND status = 1 ORDER BY name");
    $stmt->bind_param("s", $country);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $operators = [];
    while ($row = $result->fetch_assoc()) {
        $operators[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'operators' => $operators
    ]);
}

function handleGetRechargeSettings($conn) {
    global $license_key, $device_id;
    
    // Validate license and device
    if (!validateLicenseAndDevice($conn, $license_key, $device_id)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid license or device not authorized'
        ]);
        return;
    }
    
    $stmt = $conn->prepare("SELECT * FROM recharge_settings WHERE license_key = ? AND device_id = ?");
    $stmt->bind_param("ss", $license_key, $device_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $settings = $result->fetch_assoc();
        echo json_encode([
            'success' => true,
            'settings' => $settings
        ]);
    } else {
        // Return default settings
        echo json_encode([
            'success' => true,
            'settings' => [
                'license_key' => $license_key,
                'device_id' => $device_id,
                'sim1_operator' => '',
                'sim2_operator' => '',
                'auto_recharge_enabled' => 0,
                'server_url' => '',
                'api_pin' => '',
                'settings_json' => '{}'
            ]
        ]);
    }
}

function handleUpdateRechargeSettings($conn) {
    global $license_key, $device_id;
    
    // Validate license and device
    if (!validateLicenseAndDevice($conn, $license_key, $device_id)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid license or device not authorized'
        ]);
        return;
    }
    
    $sim1_operator = $_POST['sim1_operator'] ?? '';
    $sim2_operator = $_POST['sim2_operator'] ?? '';
    $auto_recharge_enabled = $_POST['auto_recharge_enabled'] ?? 0;
    $server_url = $_POST['server_url'] ?? '';
    $api_pin = $_POST['api_pin'] ?? '';
    $settings_json = $_POST['settings_json'] ?? '{}';
    
    // Insert or update settings
    $stmt = $conn->prepare("INSERT INTO recharge_settings (license_key, device_id, sim1_operator, sim2_operator, auto_recharge_enabled, server_url, api_pin, settings_json) VALUES (?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE sim1_operator = VALUES(sim1_operator), sim2_operator = VALUES(sim2_operator), auto_recharge_enabled = VALUES(auto_recharge_enabled), server_url = VALUES(server_url), api_pin = VALUES(api_pin), settings_json = VALUES(settings_json), updated_at = CURRENT_TIMESTAMP");
    $stmt->bind_param("ssssssss", $license_key, $device_id, $sim1_operator, $sim2_operator, $auto_recharge_enabled, $server_url, $api_pin, $settings_json);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'Recharge settings updated successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update recharge settings'
        ]);
    }
}

function handleGetRechargeHistory($conn) {
    global $license_key, $device_id;
    
    // Validate license and device
    if (!validateLicenseAndDevice($conn, $license_key, $device_id)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid license or device not authorized'
        ]);
        return;
    }
    
    $limit = $_GET['limit'] ?? 50;
    $offset = $_GET['offset'] ?? 0;
    
    $stmt = $conn->prepare("SELECT * FROM recharge_logs WHERE license_key = ? AND device_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?");
    $stmt->bind_param("ssii", $license_key, $device_id, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $history = [];
    while ($row = $result->fetch_assoc()) {
        $history[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'history' => $history
    ]);
}

function handleProcessSmsResponse($conn) {
    global $device_id;
    
    $sender = $_POST['sender'] ?? '';
    $message_body = $_POST['message_body'] ?? '';
    $order_id = $_POST['order_id'] ?? '';
    
    if (empty($sender) || empty($message_body)) {
        echo json_encode([
            'success' => false,
            'message' => 'Sender and message body required'
        ]);
        return;
    }
    
    // Insert SMS log
    $stmt = $conn->prepare("INSERT INTO recharge_sms_logs (device_id, sender, message_body, order_id) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $device_id, $sender, $message_body, $order_id);
    $stmt->execute();
    
    // Try to match with existing recharge order
    if (!empty($order_id)) {
        $stmt = $conn->prepare("UPDATE recharge_logs SET sms_response = ?, status = 'completed', updated_at = CURRENT_TIMESTAMP WHERE order_id = ? AND device_id = ?");
        $stmt->bind_param("sss", $message_body, $order_id, $device_id);
        $stmt->execute();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'SMS response processed successfully'
    ]);
}

// Close database connection
$conn->close();
?>
