<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#f5f5f5"
    tools:context=".recharge.RechargeActivity">

    <!-- Scrollable Content Area -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="32dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📱 Mobile Recharge"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="#2c3e50"
                    android:layout_marginBottom="8dp"
                    android:gravity="center" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Submit mobile recharge requests"
                    android:textSize="14sp"
                    android:textColor="#7f8c8d"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Recharge Form Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="💳 Recharge Details"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="16dp" />

                    <!-- Phone Number Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="📞 Phone Number">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/phoneNumberField"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="phone"
                            android:maxLength="15" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Amount Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="💰 Amount (BDT)">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/amountField"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:maxLength="10" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Operator Selection -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📡 Select Operator"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="8dp" />

                    <Spinner
                        android:id="@+id/operatorSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/spinner_background"
                        android:padding="12dp" />

                    <!-- SIM Slot Selection -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📱 Select SIM Slot"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="8dp" />

                    <Spinner
                        android:id="@+id/simSlotSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="24dp"
                        android:background="@drawable/spinner_background"
                        android:padding="12dp" />

                    <!-- Submit Button -->
                    <Button
                        android:id="@+id/submitRechargeButton"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:text="🚀 Submit Recharge"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#ffffff"
                        android:background="@drawable/button_background"
                        android:elevation="2dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📊 Status"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/statusText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Ready to submit recharge"
                        android:textSize="14sp"
                        android:textColor="#2c3e50"
                        android:background="#ecf0f1"
                        android:padding="12dp"
                        android:radius="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Instructions Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ℹ️ Instructions"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="1. Enter the phone number to recharge\n2. Enter the recharge amount\n3. Select the mobile operator\n4. Choose the SIM slot to use\n5. Click Submit to process the recharge\n\nThe app will automatically dial the USSD code for you."
                        android:textSize="12sp"
                        android:textColor="#7f8c8d"
                        android:lineSpacingExtra="2dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Back Button -->
            <Button
                android:id="@+id/backButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="🔙 Back to Dashboard"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#ffffff"
                android:background="@drawable/button_background"
                android:elevation="2dp" />

        </LinearLayout>

    </ScrollView>

    <!-- Footer Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#f5f5f5"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="📱 Mobile Recharge Module - License Management System"
            android:textSize="12sp"
            android:textColor="#7f8c8d"
            android:gravity="center"
            android:padding="8dp" />

    </LinearLayout>

</LinearLayout>
