[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_modern_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\modern_edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\layout_activity_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\layout\\activity_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_domain_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\domain_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_footer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\footer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_modern_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\modern_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\gradient_background.xml"}, {"merged": "com.mdsadrulhasan.appy99lisence.app-debug-32:/layout_activity_dashboard.xml.flat", "source": "com.mdsadrulhasan.appy99lisence.app-main-34:/layout/activity_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_modern_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\modern_status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "com.mdsadrulhasan.appy99lisence.app-debug-32:/layout_activity_recharge.xml.flat", "source": "com.mdsadrulhasan.appy99lisence.app-main-34:/layout/activity_recharge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\drawable_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\drawable\\edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-debug-32:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-34:\\mipmap-xxhdpi\\ic_launcher.webp"}]