package com.mdsadrulhasan.appy99lisence.recharge;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.mdsadrulhasan.appy99lisence.R;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Main recharge activity for submitting mobile recharge requests
 */
public class RechargeActivity extends AppCompatActivity {
    
    private static final String TAG = "RechargeActivity";
    
    // UI Components
    private EditText phoneNumberField;
    private EditText amountField;
    private Spinner operatorSpinner;
    private Spinner simSlotSpinner;
    private Button submitRechargeButton;
    private Button backButton;
    private TextView statusText;
    
    // Data
    private String licenseKey;
    private String deviceId;
    private String domainUrl;
    private SharedPreferences preferences;
    private RechargeApiClient apiClient;
    private RechargeDbHelper dbHelper;
    private USSDDialer ussdDialer;
    
    // Operators data
    private List<String> operatorNames;
    private List<String> operatorCodes;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recharge);
        
        // Initialize components
        initializeComponents();
        
        // Get intent data
        extractIntentData();
        
        // Initialize UI
        initializeUI();
        
        // Load operators
        loadOperators();
        
        Log.d(TAG, "RechargeActivity created");
    }
    
    private void initializeComponents() {
        preferences = PreferenceManager.getDefaultSharedPreferences(this);
        apiClient = new RechargeApiClient(this);
        dbHelper = new RechargeDbHelper(this);
        ussdDialer = new USSDDialer(this);
        
        operatorNames = new ArrayList<>();
        operatorCodes = new ArrayList<>();
    }
    
    private void extractIntentData() {
        Intent intent = getIntent();
        licenseKey = intent.getStringExtra("license_key");
        deviceId = intent.getStringExtra("device_id");
        domainUrl = intent.getStringExtra("domain_url");
        
        // Fallback to preferences if not in intent
        if (licenseKey == null) licenseKey = preferences.getString("license_key", "");
        if (deviceId == null) deviceId = preferences.getString("device_id", "");
        if (domainUrl == null) domainUrl = preferences.getString("domain_url", "");
        
        Log.d(TAG, "License: " + (licenseKey != null ? licenseKey.substring(0, Math.min(8, licenseKey.length())) + "..." : "null"));
    }
    
    private void initializeUI() {
        phoneNumberField = findViewById(R.id.phoneNumberField);
        amountField = findViewById(R.id.amountField);
        operatorSpinner = findViewById(R.id.operatorSpinner);
        simSlotSpinner = findViewById(R.id.simSlotSpinner);
        submitRechargeButton = findViewById(R.id.submitRechargeButton);
        backButton = findViewById(R.id.backButton);
        statusText = findViewById(R.id.statusText);
        
        // Setup SIM slot spinner
        setupSimSlotSpinner();
        
        // Setup button listeners
        setupButtonListeners();
        
        // Update status
        updateStatus("Ready to submit recharge");
    }
    
    private void setupSimSlotSpinner() {
        List<String> simSlots = new ArrayList<>();
        simSlots.add("SIM 1");
        simSlots.add("SIM 2");
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, simSlots);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        simSlotSpinner.setAdapter(adapter);
    }
    
    private void setupButtonListeners() {
        submitRechargeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                submitRecharge();
            }
        });
        
        backButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }
    
    private void loadOperators() {
        updateStatus("Loading operators...");
        
        apiClient.getOperators("BD", new RechargeApiClient.RechargeCallback() {
            @Override
            public void onResponse(JSONObject response) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (response.getBoolean("success")) {
                                JSONArray operators = response.getJSONArray("operators");
                                
                                operatorNames.clear();
                                operatorCodes.clear();
                                
                                for (int i = 0; i < operators.length(); i++) {
                                    JSONObject operator = operators.getJSONObject(i);
                                    operatorNames.add(operator.getString("name"));
                                    operatorCodes.add(operator.getString("code"));
                                }
                                
                                setupOperatorSpinner();
                                updateStatus("Operators loaded successfully");
                                
                            } else {
                                updateStatus("Failed to load operators: " + response.getString("message"));
                                loadDefaultOperators();
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error parsing operators response", e);
                            updateStatus("Error loading operators");
                            loadDefaultOperators();
                        }
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.e(TAG, "Error loading operators: " + error);
                        updateStatus("Error loading operators, using defaults");
                        loadDefaultOperators();
                    }
                });
            }
        });
    }
    
    private void loadDefaultOperators() {
        operatorNames.clear();
        operatorCodes.clear();
        
        operatorNames.add("Grameenphone");
        operatorNames.add("Robi");
        operatorNames.add("Banglalink");
        operatorNames.add("Airtel");
        operatorNames.add("Teletalk");
        
        operatorCodes.add("GP");
        operatorCodes.add("ROBI");
        operatorCodes.add("BL");
        operatorCodes.add("AIRTEL");
        operatorCodes.add("TT");
        
        setupOperatorSpinner();
    }
    
    private void setupOperatorSpinner() {
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, operatorNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        operatorSpinner.setAdapter(adapter);
    }
    
    private void submitRecharge() {
        // Validate input
        String phoneNumber = phoneNumberField.getText().toString().trim();
        String amountStr = amountField.getText().toString().trim();
        
        if (phoneNumber.isEmpty()) {
            Toast.makeText(this, "Please enter phone number", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (amountStr.isEmpty()) {
            Toast.makeText(this, "Please enter amount", Toast.LENGTH_SHORT).show();
            return;
        }
        
        double amount;
        try {
            amount = Double.parseDouble(amountStr);
            if (amount <= 0) {
                Toast.makeText(this, "Please enter valid amount", Toast.LENGTH_SHORT).show();
                return;
            }
        } catch (NumberFormatException e) {
            Toast.makeText(this, "Please enter valid amount", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (operatorSpinner.getSelectedItemPosition() < 0) {
            Toast.makeText(this, "Please select operator", Toast.LENGTH_SHORT).show();
            return;
        }
        
        String operatorCode = operatorCodes.get(operatorSpinner.getSelectedItemPosition());
        int simSlot = simSlotSpinner.getSelectedItemPosition() + 1;
        
        // Disable button and show progress
        submitRechargeButton.setEnabled(false);
        updateStatus("Submitting recharge request...");
        
        // Submit recharge
        apiClient.submitRecharge(phoneNumber, amount, operatorCode, simSlot, new RechargeApiClient.RechargeCallback() {
            @Override
            public void onResponse(JSONObject response) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (response.getBoolean("success")) {
                                String orderId = response.getString("order_id");
                                String ussdCode = response.getString("ussd_code");
                                
                                // Save to local database
                                dbHelper.insertRechargeLog(orderId, phoneNumber, amount, operatorCode, ussdCode, simSlot);
                                
                                updateStatus("Recharge submitted successfully! Order ID: " + orderId);
                                Toast.makeText(RechargeActivity.this, "Recharge submitted: " + orderId, Toast.LENGTH_LONG).show();
                                
                                // Execute USSD code
                                ussdDialer.executeRecharge(ussdCode, simSlot, orderId);
                                
                                // Clear form
                                clearForm();
                                
                            } else {
                                updateStatus("Failed to submit recharge: " + response.getString("message"));
                                Toast.makeText(RechargeActivity.this, "Failed: " + response.getString("message"), Toast.LENGTH_LONG).show();
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error parsing recharge response", e);
                            updateStatus("Error processing recharge response");
                            Toast.makeText(RechargeActivity.this, "Error processing response", Toast.LENGTH_SHORT).show();
                        }
                        
                        submitRechargeButton.setEnabled(true);
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.e(TAG, "Error submitting recharge: " + error);
                        updateStatus("Error submitting recharge: " + error);
                        Toast.makeText(RechargeActivity.this, "Error: " + error, Toast.LENGTH_LONG).show();
                        submitRechargeButton.setEnabled(true);
                    }
                });
            }
        });
    }
    
    private void clearForm() {
        phoneNumberField.setText("");
        amountField.setText("");
        operatorSpinner.setSelection(0);
        simSlotSpinner.setSelection(0);
    }
    
    private void updateStatus(String status) {
        if (statusText != null) {
            statusText.setText(status);
        }
        Log.d(TAG, "Status: " + status);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (apiClient != null) {
            apiClient.cleanup();
        }
        if (dbHelper != null) {
            dbHelper.close();
        }
        if (ussdDialer != null) {
            ussdDialer.cleanup();
        }
    }
}
